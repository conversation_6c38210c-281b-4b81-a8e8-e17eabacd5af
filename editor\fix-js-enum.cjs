const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复枚举错误...');

const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

if (!fs.existsSync(assetsDir)) {
    console.error('❌ assets 目录不存在');
    process.exit(1);
}

// 查找主 JavaScript 文件
const files = fs.readdirSync(assetsDir);
const mainJsFile = files.find(file => file.startsWith('main-') && file.endsWith('.js'));

if (!mainJsFile) {
    console.error('❌ 找不到主 JavaScript 文件');
    process.exit(1);
}

const mainJsPath = path.join(assetsDir, mainJsFile);
console.log(`📁 找到主文件: ${mainJsFile}`);

try {
    // 读取文件内容
    let content = fs.readFileSync(mainJsPath, 'utf8');
    console.log(`📖 文件大小: ${content.length} 字符`);
    
    // 记录修复前的问题
    const beforeMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 发现 li.VIE 引用: ${beforeMatches ? beforeMatches.length : 0} 个`);
    
    // 修复被压缩的枚举引用
    let fixCount = 0;
    
    // 修复 li.VIE -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIE\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复 li.VIEWER -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIEWER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复其他可能的压缩问题
    content = content.replace(/\bli\.EDI\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.EDITOR\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.ADM\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.ADMIN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.OWN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });
    
    content = content.replace(/\bli\.OWNER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });
    
    // 在文件开头添加枚举定义
    const enumDefinition = `
// 🚀 枚举修复脚本 - 解决压缩后的枚举引用问题
(function() {
    'use strict';

    // 安全地定义枚举，避免覆盖已存在的对象
    if (!window.CollaborationRole) {
        window.CollaborationRole = {
            VIEWER: 'viewer',
            EDITOR: 'editor',
            ADMIN: 'admin',
            OWNER: 'owner'
        };
    }

    if (!window.Permission) {
        window.Permission = {
            VIEW_SCENE: 'view_scene',
            EDIT_SCENE: 'edit_scene',
            CREATE_ENTITY: 'create_entity',
            UPDATE_ENTITY: 'update_entity',
            DELETE_ENTITY: 'delete_entity',
            ADD_COMPONENT: 'add_component',
            UPDATE_COMPONENT: 'update_component',
            REMOVE_COMPONENT: 'remove_component',
            UPLOAD_ASSET: 'upload_asset',
            SAVE_SCENE: 'save_scene',
            EXPORT_SCENE: 'export_scene',
            IMPORT_SCENE: 'import_scene',
            ASSIGN_ROLES: 'assign_roles',
            MANAGE_PERMISSIONS: 'manage_permissions'
        };
    }

    // 创建 li 对象来处理压缩后的引用，使用 Proxy 来动态处理
    if (!window.li) {
        window.li = new Proxy({}, {
            get: function(target, prop) {
                // 映射压缩后的属性名到正确的值
                const mapping = {
                    'VIE': 'viewer',
                    'VIEWER': 'viewer',
                    'EDI': 'editor',
                    'EDITOR': 'editor',
                    'ADM': 'admin',
                    'ADMIN': 'admin',
                    'OWN': 'owner',
                    'OWNER': 'owner'
                };

                if (prop in mapping) {
                    return mapping[prop];
                }

                // 如果没有找到映射，返回 viewer 作为默认值
                return 'viewer';
            },
            set: function(target, prop, value) {
                // 阻止设置属性，避免只读错误
                console.warn('尝试设置 li.' + prop + ' = ' + value + '，已被阻止');
                return true;
            }
        });
    }

    console.log('✅ 枚举修复完成');
})();

`;
    
    content = enumDefinition + content;
    
    // 写回文件
    fs.writeFileSync(mainJsPath, content, 'utf8');
    
    console.log(`✅ 修复完成! 共修复 ${fixCount} 个引用`);
    console.log(`📝 文件已更新: ${mainJsPath}`);

    // 验证修复结果
    const afterMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 修复后剩余 li.VIE 引用: ${afterMatches ? afterMatches.length : 0} 个`);

    // 更新 HTML 文件，注入修复脚本
    const htmlPath = path.join(distDir, 'index.html');
    if (fs.existsSync(htmlPath)) {
        console.log('📄 更新 HTML 文件...');
        let htmlContent = fs.readFileSync(htmlPath, 'utf8');

        // 检查是否已经注入过修复脚本
        if (!htmlContent.includes('枚举修复脚本')) {
            // 在 </head> 标签前注入修复脚本
            const fixScript = `
    <!-- 枚举修复脚本 -->
    <script>
${enumDefinition.replace(/^\s*\/\/ 🚀.*?\n/, '').replace(/^\s*\(function\(\) \{\s*\n\s*'use strict';\s*\n/, '').replace(/\s*\}\)\(\);\s*$/, '').trim()}
    </script>`;

            htmlContent = htmlContent.replace('</head>', fixScript + '\n  </head>');
            fs.writeFileSync(htmlPath, htmlContent, 'utf8');
            console.log('✅ HTML 文件已更新');
        } else {
            console.log('ℹ️ HTML 文件已包含修复脚本');
        }
    }

} catch (error) {
    console.error('❌ 修复过程中出错:', error);
    process.exit(1);
}

console.log('🎉 枚举修复脚本执行完成!');
