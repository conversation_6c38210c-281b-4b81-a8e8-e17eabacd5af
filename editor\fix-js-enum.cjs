const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复枚举错误...');

const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

if (!fs.existsSync(assetsDir)) {
    console.error('❌ assets 目录不存在');
    process.exit(1);
}

// 查找主 JavaScript 文件
const files = fs.readdirSync(assetsDir);
const mainJsFile = files.find(file => file.startsWith('main-') && file.endsWith('.js'));

if (!mainJsFile) {
    console.error('❌ 找不到主 JavaScript 文件');
    process.exit(1);
}

const mainJsPath = path.join(assetsDir, mainJsFile);
console.log(`📁 找到主文件: ${mainJsFile}`);

try {
    // 读取文件内容
    let content = fs.readFileSync(mainJsPath, 'utf8');
    console.log(`📖 文件大小: ${content.length} 字符`);
    
    // 记录修复前的问题
    const beforeMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 发现 li.VIE 引用: ${beforeMatches ? beforeMatches.length : 0} 个`);
    
    // 修复被压缩的枚举引用
    let fixCount = 0;
    
    // 修复 li.VIE -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIE\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复 li.VIEWER -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIEWER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复其他可能的压缩问题
    content = content.replace(/\bli\.EDI\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.EDITOR\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.ADM\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.ADMIN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.OWN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });
    
    content = content.replace(/\bli\.OWNER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });
    
    // 在文件开头添加枚举定义
    const enumDefinition = `
// 🚀 超级强化的枚举修复脚本
console.log('🔧 启动超级枚举修复...');

// 第一步：立即定义所有枚举
window.CollaborationRole = Object.freeze({
    VIEWER: 'viewer',
    EDITOR: 'editor',
    ADMIN: 'admin',
    OWNER: 'owner'
});

window.Permission = Object.freeze({
    VIEW_SCENE: 'view_scene',
    EDIT_SCENE: 'edit_scene',
    CREATE_ENTITY: 'create_entity',
    UPDATE_ENTITY: 'update_entity',
    DELETE_ENTITY: 'delete_entity',
    ADD_COMPONENT: 'add_component',
    UPDATE_COMPONENT: 'update_component',
    REMOVE_COMPONENT: 'remove_component',
    UPLOAD_ASSET: 'upload_asset',
    SAVE_SCENE: 'save_scene',
    EXPORT_SCENE: 'export_scene',
    IMPORT_SCENE: 'import_scene',
    ASSIGN_ROLES: 'assign_roles',
    MANAGE_PERMISSIONS: 'manage_permissions'
});

// 第二步：创建强化的 li 对象
window.li = new Proxy({
    VIE: 'viewer',
    VIEWER: 'viewer',
    EDITOR: 'editor',
    ADMIN: 'admin',
    OWNER: 'owner'
}, {
    get: function(target, prop) {
        if (prop === 'VIE' || prop === 'VIEWER') {
            console.log('✅ 代理成功拦截 li.' + prop);
            return 'viewer';
        }
        return target[prop] || 'viewer'; // 默认返回 viewer
    }
});

console.log('✅ 超级枚举修复完成!');
console.log('测试结果:');
console.log('- CollaborationRole.VIEWER =', window.CollaborationRole.VIEWER);
console.log('- li.VIE =', window.li.VIE);
console.log('- li.VIEWER =', window.li.VIEWER);

`;
    
    content = enumDefinition + content;
    
    // 写回文件
    fs.writeFileSync(mainJsPath, content, 'utf8');
    
    console.log(`✅ 修复完成! 共修复 ${fixCount} 个引用`);
    console.log(`📝 文件已更新: ${mainJsPath}`);
    
    // 验证修复结果
    const afterMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 修复后剩余 li.VIE 引用: ${afterMatches ? afterMatches.length : 0} 个`);
    
} catch (error) {
    console.error('❌ 修复过程中出错:', error);
    process.exit(1);
}

console.log('🎉 枚举修复脚本执行完成!');
