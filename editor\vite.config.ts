import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  root: '.',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    chunkSizeWarningLimit: 1000,
    minify: 'terser',
    terserOptions: {
      compress: {
        // 防止枚举被错误压缩
        keep_fnames: true,
        keep_classnames: true,
        // 保留对象属性名
        properties: false,
        // 不要压缩枚举相关的代码
        pure_getters: false,
        unsafe: false,
        unsafe_comps: false,
        unsafe_Function: false,
        unsafe_math: false,
        unsafe_symbols: false,
        unsafe_methods: false,
        unsafe_proto: false,
        unsafe_regexp: false,
        unsafe_undefined: false,
      },
      mangle: {
        // 保留枚举相关的属性名
        properties: false,
        keep_fnames: true,
        keep_classnames: true,
        reserved: ['CollaborationRole', 'VIEWER', 'EDITOR', 'ADMIN', 'OWNER', 'Permission', 'li']
      }
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      external: [],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'antd'],
          three: ['three'],
          physics: ['cannon-es'],
          engine: ['dl-engine-core']
        }
      },
      onwarn(warning, warn) {
        // 忽略模块导出相关的警告
        if (warning.code === 'UNRESOLVED_IMPORT' ||
            warning.code === 'MISSING_EXPORT' ||
            (warning.message && (
              warning.message.includes('cannon-es') ||
              warning.message.includes('GLTFLoader') ||
              warning.message.includes('DRACOLoader') ||
              warning.message.includes('KTX2Loader') ||
              warning.message.includes('GLTFExporter') ||
              warning.message.includes('Compound')
            ))) {
          return;
        }
        warn(warning);
      }
    }
  },
  optimizeDeps: {
    include: [
      'cannon-es',
      'three',
      'three/examples/jsm/loaders/GLTFLoader',
      'three/examples/jsm/loaders/DRACOLoader',
      'three/examples/jsm/loaders/KTX2Loader',
      'three/examples/jsm/exporters/GLTFExporter'
    ],
    exclude: []
  },
  ssr: {
    noExternal: ['cannon-es', 'three']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: '@import "./src/styles/variables.less";',
      },
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        ws: true,
      },
      '/ws': {
        target: 'ws://localhost:3007',
        ws: true,
        changeOrigin: true,
      },
    },
  },
});
