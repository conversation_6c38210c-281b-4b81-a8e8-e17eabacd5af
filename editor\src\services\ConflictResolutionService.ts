/**
 * 冲突解决服务
 * 负责检测和解决协作编辑中的操作冲突
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  addConflict,
  resolveConflict,
  setConflicts
} from '../store/collaboration/conflictSlice';
import { Operation, OperationType } from '../types/collaboration';
import { collaborationService } from './CollaborationService';
import { recursiveMergeService, MergeStrategy } from './RecursiveMergeService';
import { conflictVisualizationService } from './ConflictVisualizationService';
import { aiConflictResolver } from './AIConflictResolver';

// 冲突类型枚举
export enum ConflictType {
  ENTITY_CONFLICT = 'entity_conflict',
  COMPONENT_CONFLICT = 'component_conflict',
  PROPERTY_CONFLICT = 'property_conflict',
  DELETION_CONFLICT = 'deletion_conflict',
  SCENE_CONFLICT = 'scene_conflict'
}

// 冲突状态枚举
export enum ConflictStatus {
  PENDING = 'pending',
  RESOLVED = 'resolved',
  IGNORED = 'ignored'
}

// 冲突解决策略枚举
export enum ConflictResolutionStrategy {
  ACCEPT_LOCAL = 'accept_local',
  ACCEPT_REMOTE = 'accept_remote',
  MERGE = 'merge',
  CUSTOM = 'custom'
}

// 冲突接口
export interface Conflict {
  id: string;
  type: ConflictType;
  status: ConflictStatus;
  localOperation: Operation;
  remoteOperation: Operation;
  entityId?: string;
  componentId?: string;
  propertyPath?: string[];
  createdAt: number;
  resolvedAt?: number;
  resolution?: ConflictResolutionStrategy;
  customResolution?: any;
}

/**
 * 冲突解决服务类
 */
class ConflictResolutionService extends EventEmitter {
  private conflicts: Map<string, Conflict> = new Map();
  private operationBuffer: Operation[] = [];
  private enabled: boolean = true;
  private autoResolveSimpleConflicts: boolean = true;
  private useAIAssistance: boolean = false;

  constructor() {
    super();

    // 监听协作服务的操作事件
    collaborationService.on('operation', this.handleRemoteOperation.bind(this));
  }

  /**
   * 设置是否启用冲突解决
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 设置是否自动解决简单冲突
   * @param autoResolve 是否自动解决
   */
  public setAutoResolveSimpleConflicts(autoResolve: boolean): void {
    this.autoResolveSimpleConflicts = autoResolve;
  }

  /**
   * 设置是否使用AI辅助
   * @param useAI 是否使用AI辅助
   */
  public setUseAIAssistance(useAI: boolean): void {
    this.useAIAssistance = useAI;

    // 如果启用AI辅助，初始化AI冲突解决器
    if (useAI) {
      aiConflictResolver.setConfig({ enabled: true });
    } else {
      aiConflictResolver.setConfig({ enabled: false });
    }
  }

  /**
   * 处理本地操作
   * @param operation 操作
   * @returns 是否有冲突
   */
  public handleLocalOperation(operation: Operation): boolean {
    if (!this.enabled) {
      return false;
    }

    // 检查是否与缓冲区中的远程操作冲突
    const conflict = this.detectConflict(operation, this.operationBuffer);

    if (conflict) {
      // 添加冲突
      this.addConflict(conflict);
      return true;
    }

    return false;
  }

  /**
   * 处理远程操作
   * @param operation 操作
   */
  private handleRemoteOperation(operation: Operation): void {
    if (!this.enabled) {
      return;
    }

    // 添加到操作缓冲区
    this.operationBuffer.push(operation);

    // 限制缓冲区大小
    if (this.operationBuffer.length > 100) {
      this.operationBuffer = this.operationBuffer.slice(-100);
    }
  }

  /**
   * 检测冲突
   * @param localOperation 本地操作
   * @param remoteOperations 远程操作列表
   * @returns 冲突对象，如果没有冲突则返回null
   */
  private detectConflict(localOperation: Operation, remoteOperations: Operation[]): Conflict | null {
    // 实现冲突检测逻辑
    for (const remoteOperation of remoteOperations) {
      // 检查是否是同一实体的操作
      if (this.operationsConflict(localOperation, remoteOperation)) {
        return this.createConflict(localOperation, remoteOperation);
      }
    }

    return null;
  }

  /**
   * 判断两个操作是否冲突
   * @param op1 操作1
   * @param op2 操作2
   * @returns 是否冲突
   */
  private operationsConflict(op1: Operation, op2: Operation): boolean {
    // 如果是同一个用户的操作，不冲突
    if (op1.userId === op2.userId) {
      return false;
    }

    // 如果操作时间相差太远（超过5分钟），不认为是冲突
    const timeDiff = Math.abs(op1.timestamp - op2.timestamp);
    if (timeDiff > 5 * 60 * 1000) {
      return false;
    }

    // 根据操作类型判断是否冲突
    switch (op1.type) {
      case OperationType.ENTITY_CREATE:
      case OperationType.ENTITY_UPDATE:
      case OperationType.ENTITY_DELETE:
        return this.entityOperationsConflict(op1, op2);

      case OperationType.COMPONENT_ADD:
      case OperationType.COMPONENT_UPDATE:
      case OperationType.COMPONENT_REMOVE:
        return this.componentOperationsConflict(op1, op2);

      case OperationType.SCENE_UPDATE:
        return this.sceneOperationsConflict(op1, op2);

      case OperationType.PROPERTY_UPDATE:
        return this.propertyOperationsConflict(op1, op2);

      // 光标移动和选择变更不会产生冲突
      case OperationType.CURSOR_MOVE:
      case OperationType.SELECTION_CHANGE:
        return false;

      default:
        return false;
    }
  }

  /**
   * 判断实体操作是否冲突
   */
  private entityOperationsConflict(op1: Operation, op2: Operation): boolean {
    // 如果不是实体相关操作，不冲突
    if (![OperationType.ENTITY_CREATE, OperationType.ENTITY_UPDATE, OperationType.ENTITY_DELETE].includes(op2.type)) {
      return false;
    }

    // 检查是否是同一实体
    return op1.data.id === op2.data.id;
  }

  /**
   * 判断组件操作是否冲突
   */
  private componentOperationsConflict(op1: Operation, op2: Operation): boolean {
    // 如果不是组件相关操作，不冲突
    if (![OperationType.COMPONENT_ADD, OperationType.COMPONENT_UPDATE, OperationType.COMPONENT_REMOVE].includes(op2.type)) {
      return false;
    }

    // 检查是否是同一实体的同一组件
    return op1.data.entityId === op2.data.entityId && op1.data.componentType === op2.data.componentType;
  }

  /**
   * 判断场景操作是否冲突
   */
  private sceneOperationsConflict(op1: Operation, op2: Operation): boolean {
    // 如果不是场景更新操作，不冲突
    if (op2.type !== OperationType.SCENE_UPDATE) {
      return false;
    }

    // 检查是否修改了相同的场景属性
    // 如果两个操作都是场景更新，则认为冲突
    return op1.type === OperationType.SCENE_UPDATE;
  }

  /**
   * 判断属性操作是否冲突
   */
  private propertyOperationsConflict(op1: Operation, op2: Operation): boolean {
    // 如果不是属性更新操作，不冲突
    if (op2.type !== OperationType.PROPERTY_UPDATE) {
      return false;
    }

    // 检查是否是同一实体的同一属性
    return op1.data.entityId === op2.data.entityId &&
           op1.data.componentId === op2.data.componentId &&
           this.isSamePropertyPath(op1.data.propertyPath, op2.data.propertyPath);
  }

  /**
   * 创建冲突对象
   */
  private createConflict(localOperation: Operation, remoteOperation: Operation): Conflict {
    const conflictType = this.getConflictType(localOperation, remoteOperation);

    const conflict: Conflict = {
      id: this.generateId(),
      type: conflictType,
      status: ConflictStatus.PENDING,
      localOperation,
      remoteOperation,
      createdAt: Date.now()
    };

    // 根据操作类型设置额外信息
    switch (conflictType) {
      case ConflictType.ENTITY_CONFLICT:
        conflict.entityId = localOperation.data.id;
        break;

      case ConflictType.COMPONENT_CONFLICT:
        conflict.entityId = localOperation.data.entityId;
        conflict.componentId = localOperation.data.componentId;
        break;

      case ConflictType.PROPERTY_CONFLICT:
        conflict.entityId = localOperation.data.entityId;
        conflict.componentId = localOperation.data.componentId;
        conflict.propertyPath = localOperation.data.propertyPath;
        break;
    }

    return conflict;
  }

  /**
   * 获取冲突类型
   */
  private getConflictType(localOperation: Operation, remoteOperation: Operation): ConflictType {
    if (localOperation.type === OperationType.ENTITY_DELETE || remoteOperation.type === OperationType.ENTITY_DELETE) {
      return ConflictType.DELETION_CONFLICT;
    }

    if ([OperationType.ENTITY_CREATE, OperationType.ENTITY_UPDATE].includes(localOperation.type)) {
      return ConflictType.ENTITY_CONFLICT;
    }

    if ([OperationType.COMPONENT_ADD, OperationType.COMPONENT_UPDATE, OperationType.COMPONENT_REMOVE].includes(localOperation.type)) {
      return ConflictType.COMPONENT_CONFLICT;
    }

    if (localOperation.type === OperationType.SCENE_UPDATE) {
      return ConflictType.SCENE_CONFLICT;
    }

    // 默认为属性冲突
    return ConflictType.PROPERTY_CONFLICT;
  }

  /**
   * 添加冲突
   */
  private addConflict(conflict: Conflict): void {
    this.conflicts.set(conflict.id, conflict);
    store.dispatch(addConflict(conflict));

    // 发出冲突事件
    this.emit('conflict', conflict);

    // 创建冲突可视化
    conflictVisualizationService.createVisualizationForConflict(conflict);

    // 如果启用了自动解决，尝试自动解决冲突
    if (this.autoResolveSimpleConflicts) {
      if (this.useAIAssistance) {
        // 使用AI辅助解决冲突
        this.tryAIResolve(conflict);
      } else {
        // 使用基本规则解决冲突
        const resolution = this.tryAutoResolve(conflict);

        if (resolution) {
          this.resolveConflict(conflict.id, resolution);
        } else {
          // 通知用户有冲突需要手动解决
          message.warning('检测到编辑冲突，请在冲突面板中解决');
        }
      }
    } else {
      // 通知用户有冲突需要手动解决
      message.warning('检测到编辑冲突，请在冲突面板中解决');
    }
  }

  /**
   * 尝试自动解决冲突
   * @param conflict 冲突对象
   * @returns 解决策略，如果无法自动解决则返回null
   */
  private tryAutoResolve(conflict: Conflict): ConflictResolutionStrategy | null {
    const { localOperation, remoteOperation, type } = conflict;

    // 如果是光标移动或选择变更，接受远程操作
    if (
      localOperation.type === OperationType.CURSOR_MOVE ||
      localOperation.type === OperationType.SELECTION_CHANGE ||
      remoteOperation.type === OperationType.CURSOR_MOVE ||
      remoteOperation.type === OperationType.SELECTION_CHANGE
    ) {
      return ConflictResolutionStrategy.ACCEPT_REMOTE;
    }

    // 如果是属性更新操作，尝试合并
    if (
      localOperation.type === OperationType.PROPERTY_UPDATE &&
      remoteOperation.type === OperationType.PROPERTY_UPDATE
    ) {
      // 检查是否可以合并
      if (this.canMergePropertyUpdates(localOperation, remoteOperation)) {
        return ConflictResolutionStrategy.MERGE;
      }
    }

    // 如果是组件更新操作，尝试合并
    if (
      localOperation.type === OperationType.COMPONENT_UPDATE &&
      remoteOperation.type === OperationType.COMPONENT_UPDATE
    ) {
      // 检查是否可以合并
      if (this.canMergeComponentUpdates(localOperation, remoteOperation)) {
        return ConflictResolutionStrategy.MERGE;
      }
    }

    // 如果是实体更新操作，尝试合并
    if (
      localOperation.type === OperationType.ENTITY_UPDATE &&
      remoteOperation.type === OperationType.ENTITY_UPDATE
    ) {
      // 检查是否可以合并
      if (this.canMergeEntityUpdates(localOperation, remoteOperation)) {
        return ConflictResolutionStrategy.MERGE;
      }
    }

    // 如果是场景更新操作，尝试合并
    if (
      localOperation.type === OperationType.SCENE_UPDATE &&
      remoteOperation.type === OperationType.SCENE_UPDATE
    ) {
      // 检查是否可以合并
      if (this.canMergeSceneUpdates(localOperation, remoteOperation)) {
        return ConflictResolutionStrategy.MERGE;
      }
    }

    // 如果本地操作比远程操作更新，优先采用本地操作
    if (localOperation.timestamp > remoteOperation.timestamp + 5000) { // 如果本地操作比远程操作晚5秒以上
      return ConflictResolutionStrategy.ACCEPT_LOCAL;
    }

    // 如果远程操作比本地操作更新，优先采用远程操作
    if (remoteOperation.timestamp > localOperation.timestamp + 5000) { // 如果远程操作比本地操作晚5秒以上
      return ConflictResolutionStrategy.ACCEPT_REMOTE;
    }

    // 如果是删除冲突，优先采用删除操作
    if (type === ConflictType.DELETION_CONFLICT) {
      if (localOperation.type === OperationType.ENTITY_DELETE) {
        return ConflictResolutionStrategy.ACCEPT_LOCAL;
      } else if (remoteOperation.type === OperationType.ENTITY_DELETE) {
        return ConflictResolutionStrategy.ACCEPT_REMOTE;
      }
    }

    // 其他情况无法自动解决
    return null;
  }

  /**
   * 尝试使用AI解决冲突
   * @param conflict 冲突对象
   */
  private async tryAIResolve(conflict: Conflict): Promise<void> {
    try {
      // 使用AI冲突解决器分析冲突
      const result = await aiConflictResolver.autoResolve(conflict);

      // 如果AI能够解决冲突
      if (result.resolved && result.strategy) {
        // 应用AI推荐的解决策略
        this.resolveConflict(conflict.id, result.strategy, result.mergedData);

        // 通知用户AI已解决冲突
        message.success(`AI已自动解决冲突: ${result.explanation || '使用' + result.strategy}`);
      } else {
        // AI无法自动解决，通知用户手动解决
        message.warning('AI无法自动解决此冲突，请在冲突面板中手动解决');
      }
    } catch (error) {
      console.error('AI解决冲突失败:', error);

      // 回退到基本规则
      const resolution = this.tryAutoResolve(conflict);

      if (resolution) {
        this.resolveConflict(conflict.id, resolution);
      } else {
        // 通知用户有冲突需要手动解决
        message.warning('检测到编辑冲突，请在冲突面板中解决');
      }
    }
  }

  /**
   * 获取AI解决建议
   * @param conflictId 冲突ID
   * @returns 建议列表Promise
   */
  public async getAISuggestions(conflictId: string): Promise<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[]> {
    const conflict = this.conflicts.get(conflictId);

    if (!conflict) {
      throw new Error(`找不到冲突: ${conflictId}`);
    }

    // 使用AI冲突解决器获取建议
    return aiConflictResolver.getSuggestions(conflict);
  }

  /**
   * 获取AI推荐的解决策略
   * @param conflictId 冲突ID
   * @returns 推荐策略Promise
   */
  public async getAIRecommendation(conflictId: string): Promise<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
  }> {
    const conflict = this.conflicts.get(conflictId);

    if (!conflict) {
      throw new Error(`找不到冲突: ${conflictId}`);
    }

    // 使用AI冲突解决器获取推荐策略
    return aiConflictResolver.getRecommendedStrategy(conflict);
  }

  /**
   * 提供AI反馈
   * @param conflictId 冲突ID
   * @param wasHelpful 是否有帮助
   * @param appliedStrategy 应用的策略
   * @param comment 评论
   */
  public provideAIFeedback(
    conflictId: string,
    wasHelpful: boolean,
    appliedStrategy?: ConflictResolutionStrategy,
    comment?: string
  ): void {
    // 使用AI冲突解决器提供反馈
    aiConflictResolver.provideFeedback(conflictId, wasHelpful, appliedStrategy, comment);
  }

  /**
   * 解决冲突
   * @param conflictId 冲突ID
   * @param strategy 解决策略
   * @param customResolution 自定义解决数据
   */
  public resolveConflict(
    conflictId: string,
    strategy: ConflictResolutionStrategy,
    customResolution?: any
  ): void {
    const conflict = this.conflicts.get(conflictId);

    if (!conflict) {
      console.error(`找不到冲突: ${conflictId}`);
      return;
    }

    // 更新冲突状态
    conflict.status = ConflictStatus.RESOLVED;
    conflict.resolvedAt = Date.now();
    conflict.resolution = strategy;

    if (strategy === ConflictResolutionStrategy.CUSTOM) {
      conflict.customResolution = customResolution;
    }

    // 更新Redux状态
    store.dispatch(resolveConflict({
      conflictId,
      status: ConflictStatus.RESOLVED,
      resolution: strategy,
      customResolution
    }));

    // 应用解决策略
    this.applyResolution(conflict, strategy, customResolution);

    // 移除冲突可视化
    conflictVisualizationService.removeVisualizationForConflict(conflictId);

    // 发出冲突解决事件
    this.emit('conflictResolved', conflict);

    // 通知用户冲突已解决
    message.success('冲突已成功解决');
  }

  /**
   * 应用解决策略
   */
  private applyResolution(
    conflict: Conflict,
    strategy: ConflictResolutionStrategy,
    customResolution?: any
  ): void {
    switch (strategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        // 保留本地操作，忽略远程操作
        break;

      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        // 接受远程操作，放弃本地操作
        break;

      case ConflictResolutionStrategy.MERGE:
        // 合并操作
        this.mergeOperations(conflict.localOperation, conflict.remoteOperation);
        break;

      case ConflictResolutionStrategy.CUSTOM:
        // 应用自定义解决方案
        this.applyCustomResolution(conflict, customResolution);
        break;
    }
  }

  /**
   * 合并操作
   */
  private mergeOperations(localOperation: Operation, remoteOperation: Operation): void {
    // 尝试使用递归合并服务进行高级合并
    try {
      const { data, conflicts } = recursiveMergeService.mergeOperationData(
        localOperation,
        remoteOperation,
        {
          strategy: MergeStrategy.DEEP_MERGE,
          maxDepth: 10
        }
      );

      // 如果有冲突但仍然成功合并
      if (conflicts.length > 0) {
        console.log(`合并过程中发现 ${conflicts.length} 个冲突，但已自动解决`);
      }

      // 创建合并后的操作
      const mergedOperation: Operation = {
        ...localOperation,
        id: this.generateId(),
        timestamp: Date.now(),
        data
      };

      // 发送合并后的操作
      collaborationService.sendOperation({
        type: mergedOperation.type,
        data: mergedOperation.data
      });

      return;
    } catch (error) {
      console.warn('递归合并失败，回退到基本合并策略:', error);
    }

    // 如果递归合并失败，回退到基本合并策略
    switch (localOperation.type) {
      case OperationType.PROPERTY_UPDATE:
        this.mergePropertyUpdates(localOperation, remoteOperation);
        break;

      case OperationType.COMPONENT_UPDATE:
        this.mergeComponentUpdates(localOperation, remoteOperation);
        break;

      case OperationType.ENTITY_UPDATE:
        this.mergeEntityUpdates(localOperation, remoteOperation);
        break;

      case OperationType.SCENE_UPDATE:
        this.mergeSceneUpdates(localOperation, remoteOperation);
        break;

      default:
        console.warn('无法合并的操作类型:', localOperation.type);
        break;
    }
  }

  /**
   * 合并属性更新操作
   */
  private mergePropertyUpdates(localOperation: Operation, remoteOperation: Operation): void {
    // 创建合并后的操作
    const mergedOperation: Operation = {
      ...localOperation,
      id: this.generateId(),
      timestamp: Date.now()
    };

    // 发送合并后的操作
    collaborationService.sendOperation({
      type: mergedOperation.type,
      data: mergedOperation.data
    });

    // 如果远程操作修改了不同的属性，也应用它
    if (!this.isSamePropertyPath(localOperation.data?.path, remoteOperation.data?.path)) {
      collaborationService.sendOperation({
        type: remoteOperation.type,
        data: remoteOperation.data
      });
    }
  }

  /**
   * 检查两个属性路径是否相同
   */
  private isSamePropertyPath(path1: string[], path2: string[]): boolean {
    if (!path1 || !path2 || path1.length !== path2.length) {
      return false;
    }

    return path1.every((p, i) => p === path2[i]);
  }

  /**
   * 合并组件更新操作
   */
  private mergeComponentUpdates(localOperation: Operation, remoteOperation: Operation): void {
    // 获取本地和远程操作修改的属性
    const localProps = localOperation.data?.properties || {};
    const remoteProps = remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 创建合并后的操作
    const mergedOperation: Operation = {
      ...localOperation,
      id: this.generateId(),
      timestamp: Date.now(),
      data: {
        ...localOperation.data,
        properties: mergedProps
      }
    };

    // 发送合并后的操作
    collaborationService.sendOperation({
      type: mergedOperation.type,
      data: mergedOperation.data
    });
  }

  /**
   * 合并实体更新操作
   */
  private mergeEntityUpdates(localOperation: Operation, remoteOperation: Operation): void {
    // 获取本地和远程操作修改的属性
    const localProps = localOperation.data?.properties || {};
    const remoteProps = remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 创建合并后的操作
    const mergedOperation: Operation = {
      ...localOperation,
      id: this.generateId(),
      timestamp: Date.now(),
      data: {
        ...localOperation.data,
        properties: mergedProps
      }
    };

    // 发送合并后的操作
    collaborationService.sendOperation({
      type: mergedOperation.type,
      data: mergedOperation.data
    });
  }

  /**
   * 合并场景更新操作
   */
  private mergeSceneUpdates(localOperation: Operation, remoteOperation: Operation): void {
    // 获取本地和远程操作修改的属性
    const localProps = localOperation.data?.properties || {};
    const remoteProps = remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 创建合并后的操作
    const mergedOperation: Operation = {
      ...localOperation,
      id: this.generateId(),
      timestamp: Date.now(),
      data: {
        ...localOperation.data,
        properties: mergedProps
      }
    };

    // 发送合并后的操作
    collaborationService.sendOperation({
      type: mergedOperation.type,
      data: mergedOperation.data
    });
  }

  /**
   * 应用自定义解决方案
   */
  private applyCustomResolution(conflict: Conflict, customResolution: any): void {
    // 根据自定义解决方案创建新的操作
    const customOperation: Operation = {
      id: this.generateId(),
      userId: collaborationService.getUserId(),
      timestamp: Date.now(),
      type: conflict.localOperation.type,
      data: customResolution
    };

    // 发送自定义操作
    collaborationService.sendOperation({
      type: customOperation.type,
      data: customOperation.data
    });
  }

  /**
   * 检查是否可以合并属性更新操作
   * @param op1 操作1
   * @param op2 操作2
   * @returns 是否可以合并
   */
  private canMergePropertyUpdates(op1: Operation, op2: Operation): boolean {
    // 获取属性路径
    const path1 = op1.data?.path;
    const path2 = op2.data?.path;

    if (!path1 || !path2) {
      return false;
    }

    // 如果路径完全相同，检查修改时间
    if (path1.length === path2.length && path1.every((p: string, i: number) => p === path2[i])) {
      // 如果时间差距很大，选择较新的操作
      const timeDiff = Math.abs(op1.timestamp - op2.timestamp);
      if (timeDiff > 10000) { // 10秒以上的时间差
        return false;
      }

      // 如果时间接近，无法自动合并
      return false;
    }

    // 如果路径不同，可以合并
    return true;
  }

  /**
   * 检查是否可以合并组件更新操作
   * @param op1 操作1
   * @param op2 操作2
   * @returns 是否可以合并
   */
  private canMergeComponentUpdates(op1: Operation, op2: Operation): boolean {
    // 获取修改的属性
    const props1 = Object.keys(op1.data?.properties || {});
    const props2 = Object.keys(op2.data?.properties || {});

    // 检查是否有交集
    const intersection = props1.filter(prop => props2.includes(prop));

    // 如果没有交集，可以合并
    if (intersection.length === 0) {
      return true;
    }

    // 如果有交集，检查每个交集属性的值
    for (const prop of intersection) {
      const val1 = op1.data?.properties[prop];
      const val2 = op2.data?.properties[prop];

      // 如果值相同，可以合并
      if (JSON.stringify(val1) === JSON.stringify(val2)) {
        continue;
      }

      // 如果值不同，检查是否是简单类型
      if (typeof val1 === 'number' || typeof val1 === 'string' || typeof val1 === 'boolean') {
        // 简单类型，无法自动合并
        return false;
      }

      // 如果是对象或数组，检查是否可以递归合并
      if (typeof val1 === 'object' && typeof val2 === 'object' && val1 !== null && val2 !== null) {
        // 对于数组，无法自动合并
        if (Array.isArray(val1) || Array.isArray(val2)) {
          return false;
        }

        // 对于对象，检查是否有交集
        const objKeys1 = Object.keys(val1);
        const objKeys2 = Object.keys(val2);
        const objIntersection = objKeys1.filter(key => objKeys2.includes(key));

        // 如果没有交集，可以合并
        if (objIntersection.length === 0) {
          continue;
        }

        // 如果有交集，无法自动合并
        return false;
      }

      // 其他情况，无法自动合并
      return false;
    }

    // 所有交集属性都可以合并
    return true;
  }

  /**
   * 检查是否可以合并实体更新操作
   * @param op1 操作1
   * @param op2 操作2
   * @returns 是否可以合并
   */
  private canMergeEntityUpdates(op1: Operation, op2: Operation): boolean {
    // 实体更新的合并逻辑与组件更新类似
    return this.canMergeComponentUpdates(op1, op2);
  }

  /**
   * 检查是否可以合并场景更新操作
   * @param op1 操作1
   * @param op2 操作2
   * @returns 是否可以合并
   */
  private canMergeSceneUpdates(op1: Operation, op2: Operation): boolean {
    // 场景更新的合并逻辑与组件更新类似
    return this.canMergeComponentUpdates(op1, op2);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  }

  /**
   * 获取所有冲突
   */
  public getAllConflicts(): Conflict[] {
    return Array.from(this.conflicts.values());
  }

  /**
   * 获取待处理的冲突
   */
  public getPendingConflicts(): Conflict[] {
    return Array.from(this.conflicts.values()).filter(
      conflict => conflict.status === ConflictStatus.PENDING
    );
  }

  /**
   * 清除所有已解决的冲突
   */
  public clearResolvedConflicts(): void {
    for (const [id, conflict] of this.conflicts.entries()) {
      if (conflict.status !== ConflictStatus.PENDING) {
        this.conflicts.delete(id);
      }
    }

    // 更新Redux状态
    store.dispatch(setConflicts(this.getPendingConflicts()));
  }
}

// 创建单例实例
export const conflictResolutionService = new ConflictResolutionService();
